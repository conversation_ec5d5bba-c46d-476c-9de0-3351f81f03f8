/**
 * Configuration for retry behavior
 */
export interface RetryPolicy {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
  retryableErrors: readonly string[];
}

/**
 * Error types that should trigger retries
 */
export const RETRYABLE_ERROR_CODES = {
  // Spawn errors
  ENOENT: 'ENOENT',     // Command not found
  EACCES: 'EACCES',     // Permission denied
  EMFILE: 'EMFILE',     // Too many open files
  ENFILE: 'ENFILE',     // File table overflow
  EAGAIN: 'EAGAIN',     // Resource temporarily unavailable
  
  // Process errors
  SPAWN_FAILED: 'SPAWN_FAILED',
  SPAWN_TIMEOUT: 'SPAWN_TIMEOUT',
  
  // Heartbeat errors
  HEARTBEAT_TIMEOUT: 'HEARTBEAT_TIMEOUT',
  
  // File operation errors
  EBUSY: 'EBUSY',       // Resource busy
  ENOTEMPTY: 'ENOTEMPTY', // Directory not empty
} as const;

/**
 * Pre-configured retry policies for common scenarios
 */
export const RETRY_POLICIES = {
  SPAWN_RETRY_POLICY: {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 10000,
    backoffFactor: 2.0,
    retryableErrors: [
      RETRYABLE_ERROR_CODES.ENOENT,
      RETRYABLE_ERROR_CODES.EACCES,
      RETRYABLE_ERROR_CODES.EMFILE,
      RETRYABLE_ERROR_CODES.ENFILE,
      RETRYABLE_ERROR_CODES.EAGAIN,
      RETRYABLE_ERROR_CODES.SPAWN_FAILED,
      RETRYABLE_ERROR_CODES.SPAWN_TIMEOUT,
    ]
  },
  
  FILE_OPERATION_RETRY_POLICY: {
    maxRetries: 2,
    baseDelayMs: 500,
    maxDelayMs: 5000,
    backoffFactor: 1.5,
    retryableErrors: [
      RETRYABLE_ERROR_CODES.EBUSY,
      RETRYABLE_ERROR_CODES.ENOTEMPTY,
      RETRYABLE_ERROR_CODES.EAGAIN,
    ]
  },
  
  NETWORK_RETRY_POLICY: {
    maxRetries: 5,
    baseDelayMs: 2000,
    maxDelayMs: 30000,
    backoffFactor: 2.0,
    retryableErrors: [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
    ]
  }
} as const;

/**
 * Information about retry attempts
 */
export interface RetryAttemptInfo {
  attempt: number;
  totalAttempts: number;
  delay: number;
  error: Error;
  isLastAttempt: boolean;
}

/**
 * Result of a retry operation
 */
export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalDelay: number;
  errors: Error[];
}

/**
 * Intelligent retry system for handling failures with exponential backoff
 */
export class RetryManager {
  /**
   * Calculate delay for a given attempt with jitter
   */
  public static calculateDelay(attempt: number, policy: RetryPolicy): number {
    const exponentialDelay = policy.baseDelayMs * Math.pow(policy.backoffFactor, attempt - 1);
    const cappedDelay = Math.min(exponentialDelay, policy.maxDelayMs);
    
    // Add jitter (±25%) to avoid thundering herd
    const jitter = cappedDelay * 0.25 * (Math.random() - 0.5);
    return Math.max(0, cappedDelay + jitter);
  }

  /**
   * Check if an error should trigger a retry
   */
  public static isRetryableError(error: Error, policy: RetryPolicy): boolean {
    const errorCode = (error as any).code || error.name || error.message;
    return policy.retryableErrors.some(retryableCode => 
      errorCode.includes(retryableCode) || error.message.includes(retryableCode)
    );
  }

  /**
   * Execute an operation with retry logic
   */
  public static async executeWithRetry<T>(
    operation: (abortSignal?: AbortSignal) => Promise<T>,
    policy: RetryPolicy,
    abortSignal?: AbortSignal
  ): Promise<RetryResult<T>> {
    const errors: Error[] = [];
    let totalDelay = 0;
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= policy.maxRetries + 1; attempt++) {
      try {
        // Check if operation was aborted
        if (abortSignal?.aborted) {
          throw new Error('Operation aborted');
        }

        const result = await operation(abortSignal);
        
        return {
          success: true,
          result,
          attempts: attempt,
          totalDelay,
          errors
        };
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        lastError = err;
        errors.push(err);

        const isLastAttempt = attempt > policy.maxRetries;
        
        // Log retry attempt
        console.warn(`Retry attempt ${attempt}/${policy.maxRetries + 1} failed:`, {
          error: err.message,
          code: (err as any).code,
          isLastAttempt,
          willRetry: !isLastAttempt && this.isRetryableError(err, policy)
        });

        // If this is the last attempt or error is not retryable, fail
        if (isLastAttempt || !this.isRetryableError(err, policy)) {
          break;
        }

        // Calculate and apply delay
        const delay = this.calculateDelay(attempt, policy);
        totalDelay += delay;

        // Wait before retry, but check for abort signal
        await this.delay(delay, abortSignal);
      }
    }

    // Create aggregated error with all attempt information
    const aggregatedError = new Error(
      `Operation failed after ${errors.length} attempts. Last error: ${lastError?.message}`
    );
    (aggregatedError as any).attempts = errors.length;
    (aggregatedError as any).errors = errors;
    (aggregatedError as any).totalDelay = totalDelay;

    return {
      success: false,
      error: aggregatedError,
      attempts: errors.length,
      totalDelay,
      errors
    };
  }

  /**
   * Delay with abort signal support
   */
  private static async delay(ms: number, abortSignal?: AbortSignal): Promise<void> {
    return new Promise((resolve, reject) => {
      if (abortSignal?.aborted) {
        reject(new Error('Operation aborted'));
        return;
      }

      const timeout = setTimeout(resolve, ms);
      
      const abortHandler = () => {
        clearTimeout(timeout);
        reject(new Error('Operation aborted'));
      };

      abortSignal?.addEventListener('abort', abortHandler, { once: true });
      
      // Clean up listener when timeout completes
      setTimeout(() => {
        abortSignal?.removeEventListener('abort', abortHandler);
      }, ms);
    });
  }

  /**
   * Create a retry policy with custom overrides
   */
  public static createPolicy(
    basePolicy: RetryPolicy,
    overrides: Partial<RetryPolicy>
  ): RetryPolicy {
    return {
      ...basePolicy,
      ...overrides,
      retryableErrors: overrides.retryableErrors || basePolicy.retryableErrors
    };
  }

  /**
   * Wrap a function to automatically apply retry logic
   */
  public static withRetry<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    policy: RetryPolicy
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const result = await this.executeWithRetry(
        () => fn(...args),
        policy
      );
      
      if (result.success) {
        return result.result!;
      } else {
        throw result.error;
      }
    };
  }
}
