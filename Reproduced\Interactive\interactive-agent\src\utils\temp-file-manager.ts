import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { generateSessionId } from './session-id.js';
import { PROCESS_KILL_TIMEOUT_MS } from '../constants.js';

/**
 * Interface for temporary file objects with automatic cleanup
 */
export interface TempFile {
  readonly path: string;
  readonly isDisposed: boolean;
  dispose(): Promise<void>;
}

/**
 * Information about a tracked temporary resource
 */
interface TempResource {
  path: string;
  isDirectory: boolean;
  sessionId: string;
  createdAt: Date;
}

/**
 * Comprehensive temporary file management system with automatic cleanup
 */
export class TempFileManager {
  private static instance: TempFileManager | null = null;
  private readonly resources = new Map<string, TempResource>();
  private readonly finalizationRegistry: FinalizationRegistry<string>;
  private cleanupHandlersRegistered = false;

  private constructor() {
    // Setup finalization registry for garbage collection cleanup
    this.finalizationRegistry = new FinalizationRegistry((path: string) => {
      this.cleanupResource(path).catch(err => {
        console.warn(`Failed to cleanup resource during GC: ${path}`, err);
      });
    });

    this.registerCleanupHandlers();
  }

  /**
   * Get singleton instance of TempFileManager
   */
  public static getInstance(): TempFileManager {
    if (!TempFileManager.instance) {
      TempFileManager.instance = new TempFileManager();
    }
    return TempFileManager.instance;
  }

  /**
   * Create a temporary file with unique session ID
   */
  public async createTempFile(
    prefix: string,
    extension?: string,
    content?: string
  ): Promise<TempFile> {
    const sessionId = this.generateSessionId();
    const fileName = this.generateFileName(prefix, sessionId, extension);
    const filePath = path.join(os.tmpdir(), fileName);

    try {
      // Create the file
      if (content !== undefined) {
        await fs.promises.writeFile(filePath, content, 'utf8');
      } else {
        await fs.promises.writeFile(filePath, '', 'utf8');
      }

      // Register for tracking
      const resource: TempResource = {
        path: filePath,
        isDirectory: false,
        sessionId,
        createdAt: new Date()
      };
      this.resources.set(filePath, resource);

      // Create TempFile object
      const tempFile = new TempFileImpl(filePath, this);
      
      // Register for GC cleanup
      this.finalizationRegistry.register(tempFile, filePath);

      return tempFile;
    } catch (error) {
      throw new Error(`Failed to create temp file: ${error}`);
    }
  }

  /**
   * Create a temporary directory
   */
  public async createTempDir(prefix?: string): Promise<TempFile> {
    const sessionId = this.generateSessionId();
    const dirName = this.generateFileName(prefix || 'temp', sessionId);
    const dirPath = path.join(os.tmpdir(), dirName);

    try {
      await fs.promises.mkdir(dirPath, { recursive: true });

      // Register for tracking
      const resource: TempResource = {
        path: dirPath,
        isDirectory: true,
        sessionId,
        createdAt: new Date()
      };
      this.resources.set(dirPath, resource);

      // Create TempFile object
      const tempDir = new TempFileImpl(dirPath, this);
      
      // Register for GC cleanup
      this.finalizationRegistry.register(tempDir, dirPath);

      return tempDir;
    } catch (error) {
      throw new Error(`Failed to create temp directory: ${error}`);
    }
  }

  /**
   * Register an existing file or directory for cleanup
   */
  public registerForCleanup(filePath: string, sessionId?: string): void {
    if (this.resources.has(filePath)) {
      return; // Already registered
    }

    const resource: TempResource = {
      path: filePath,
      isDirectory: this.isDirectory(filePath),
      sessionId: sessionId || this.generateSessionId(),
      createdAt: new Date()
    };
    this.resources.set(filePath, resource);
  }

  /**
   * Dispose of all managed resources
   */
  public async disposeAll(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];
    
    for (const [filePath] of this.resources) {
      cleanupPromises.push(this.cleanupResource(filePath));
    }

    await Promise.allSettled(cleanupPromises);
    this.resources.clear();
  }

  /**
   * Internal method to dispose of a specific resource
   */
  public async disposeResource(filePath: string): Promise<void> {
    await this.cleanupResource(filePath);
  }

  /**
   * Generate a unique session ID using centralized utility
   */
  private generateSessionId(): string {
    return generateSessionId();
  }

  /**
   * Generate a filename with session ID
   */
  private generateFileName(prefix: string, sessionId: string, extension?: string): string {
    const ext = extension ? (extension.startsWith('.') ? extension : `.${extension}`) : '';
    return `${prefix}-${sessionId}${ext}`;
  }

  /**
   * Check if path exists and is a directory
   */
  private isDirectory(filePath: string): boolean {
    try {
      return fs.statSync(filePath).isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * Cleanup a specific resource
   */
  private async cleanupResource(filePath: string): Promise<void> {
    try {
      const resource = this.resources.get(filePath);
      if (!resource) {
        return; // Not tracked
      }

      // Check if resource still exists
      if (!fs.existsSync(filePath)) {
        this.resources.delete(filePath);
        return;
      }

      if (resource.isDirectory) {
        await fs.promises.rmdir(filePath, { recursive: true });
      } else {
        await fs.promises.unlink(filePath);
      }

      this.resources.delete(filePath);
    } catch (error) {
      // Log but don't throw - cleanup should be graceful
      console.warn(`Failed to cleanup resource: ${filePath}`, error);
    }
  }

  /**
   * Register process exit handlers for cleanup
   */
  private registerCleanupHandlers(): void {
    if (this.cleanupHandlersRegistered) {
      return;
    }

    const cleanup = () => {
      // Synchronous cleanup on exit
      for (const [filePath, resource] of this.resources) {
        try {
          if (fs.existsSync(filePath)) {
            if (resource.isDirectory) {
              fs.rmSync(filePath, { recursive: true, force: true });
            } else {
              fs.unlinkSync(filePath);
            }
          }
        } catch (error) {
          console.warn(`Failed to cleanup on exit: ${filePath}`, error);
        }
      }
    };

    let isExiting = false;

    const synchronousCleanup = () => {
      if (isExiting) return;
      isExiting = true;
      cleanup();
    };

    process.on('exit', synchronousCleanup);

    const gracefulShutdown = (signal: string) => {
      if (isExiting) return;

      console.debug(`TempFileManager: Received ${signal}, initiating graceful shutdown`);

      // Set a timeout to prevent hanging
      const shutdownTimeout = setTimeout(() => {
        console.warn('TempFileManager: Shutdown timeout reached, forcing cleanup');
        synchronousCleanup();
        process.exit(1);
      }, PROCESS_KILL_TIMEOUT_MS);

      try {
        cleanup();
        clearTimeout(shutdownTimeout);
        process.exit(0);
      } catch (error) {
        console.warn('TempFileManager: Error during graceful shutdown:', error);
        clearTimeout(shutdownTimeout);
        synchronousCleanup();
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

    this.cleanupHandlersRegistered = true;
  }
}

/**
 * Implementation of TempFile interface
 */
class TempFileImpl implements TempFile {
  private _isDisposed = false;

  constructor(
    public readonly path: string,
    private readonly manager: TempFileManager
  ) {}

  public get isDisposed(): boolean {
    return this._isDisposed;
  }

  public async dispose(): Promise<void> {
    if (this._isDisposed) {
      return; // Already disposed
    }

    this._isDisposed = true;
    await this.manager.disposeResource(this.path);
  }
}

// Export singleton instance
export const tempFileManager = TempFileManager.getInstance();
